import { useNavigate } from 'react-router-dom';
import Breadcrumb from '@/components/ui/Breadcrumb';
import SocialMediaSelect from '@/components/ui/SocialMediaSelect';
import { useState, useCallback, useEffect } from 'react';
import Select, { SelectOption } from '@/components/ui/Select';
import TextInput from '@/components/ui/TextInput';
import Button from '@/components/ui/Button';
import Collapsible from '@/components/ui/Collapsible';
import SearchDrawerInput from '@/components/ui/SearchDrawerInput';
import TimePeriodPicker from '@/components/ui/TimePeriodPicker';
import TagInput from '@/components/ui/TagInput';
import SourceInput from '@/components/ui/SourceInput';
import { CreateWidgetPayload, Widget } from '@/types/dashboard';

// Define the mapping between report types and their allowed chart types
const REPORT_CHART_MAPPING = {
  process: ['bar', 'bar_stack', 'bar_comp', 'line', 'table'],
  'statistical%post': ['badge'],
  'statistical%source': ['badge'],
  'statistical%view': ['badge'],
  'statistical%like': ['badge'],
  'statistical%comment': ['badge'],
  'statistical%retweet': ['badge'],
  top_sources: ['table', 'bar_stack_hor', 'bar_stack_ver', 'radial'],

  // برترین محتوای منتشر شده
  advance: ['list'],
  // موضوعات پرتکرار
  cloud: ['cloud', 'table', 'bar', 'tree'],
  // تحلیل احساسات محتوای منتشر شده
  sentiments: [
    'pie',
    'donut',
    'semi_pie',
    'bar_stack_hor',
    'bar_stack_ver',
    'table',
    'radar',
    'spider',
    'wind',
  ],
  // تحلیل عواطف و هیجانات محتوا
  emotion: [
    'pie',
    'donut',
    'semi_pie',
    'bar_stack_hor',
    'bar_stack_ver',
    'table',
    'radar',
    'spider',
    'wind',
  ],
  // اطلاعات آماری دسته‌های موضوعی محتوا
  categories: [
    'pie',
    'donut',
    'semi_pie',
    'bar_stack_hor',
    'bar_stack_ver',
    'table',
    'radar',
    'spider',
    'wind',
  ],
  // نسبت محتوای توهین‌آمیز
  offensive: [
    'pie',
    'donut',
    'semi_pie',
    'bar_stack_hor',
    'bar_stack_ver',
    'table',
    'radar',
    'spider',
    'wind',
  ],
  // نسبت محتوای تبلیغاتی
  advertise: [
    'pie',
    'donut',
    'semi_pie',
    'bar_stack_hor',
    'bar_stack_ver',
    'table',
    'radar',
    'spider',
    'wind',
  ],
} as const;

// Define which chart types are stack charts (only for multiple socials)
const STACK_CHART_TYPES = [
  'bar_stack',
  'bar_comp',
  'bar_stack_hor',
  'bar_stack_ver',
] as const;

const SINGLE_CHART_TYPES = [
  'table',
  'badge',
  'bar',
  'pie',
  'donut',
  'semi_pie',
  'list',
  'live_list',
  'cloud',
  'tree',
  'map_iran',
  'map_world',
  'network_graph',
  'radial',
  'radar',
  'spider',
  'wind',
] as const;

const ZERO_LVL_CHART_TYPES = ['badge'] as const;

// Define allowed platforms for each report type
const REPORT_PLATFORM_MAPPING = {
  process: ['all'],
  top_sources: ['all'],
  advance: ['all'],
  cloud: ['all'],
  'statistical%post': ['all'],
  'statistical%source': ['all'],
  'statistical%view': ['twitter', 'telegram'],
  'statistical%like': ['twitter', 'instagram', 'telegram'],
  'statistical%comment': ['twitter', 'instagram', 'telegram'],
  'statistical%retweet': ['twitter'],
  sentiments: ['twitter', 'instagram', 'telegram'],
  emotion: ['twitter', 'instagram', 'telegram'],
  categories: ['twitter', 'instagram', 'telegram'],
  offensive: ['twitter', 'instagram', 'telegram'],
  advertise: ['instagram', 'telegram'],
} as const;

// Define allowed time intervals for each report type (in minutes)
const REPORT_TIME_INTERVALS = {
  process: [1, 3, 5, 15, 30, 45, 60, 90, 120],
  top_sources: [1, 3, 5, 15, 30, 45, 60, 90, 120],
  'statistical%post': [1, 3, 5, 15, 30, 45, 60, 90, 120],
  'statistical%source': [1, 3, 5, 15, 30, 45, 60, 90, 120],
  'statistical%view': [1, 3, 5, 15, 30, 45, 60, 90, 120],
  'statistical%like': [1, 3, 5, 15, 30, 45, 60, 90, 120],
  'statistical%comment': [1, 3, 5, 15, 30, 45, 60, 90, 120],
  'statistical%retweet': [1, 3, 5, 15, 30, 45, 60, 90, 120],
  advance: [1, 3, 5, 15, 30, 45, 60, 90, 120],
  cloud: [5, 15, 30, 45, 60, 90, 120],
  sentiments: [1, 3, 5, 15, 30, 45, 60, 90, 120],
  emotion: [1, 3, 5, 15, 30, 45, 60, 90, 120],
  categories: [1, 3, 5, 15, 30, 45, 60, 90, 120],
  offensive: [1, 3, 5, 15, 30, 45, 60, 90, 120],
  advertise: [1, 3, 5, 15, 30, 45, 60, 90, 120],
} as const;

// Define sorting fields for reports that need them
const REPORT_SORTING_FIELDS = {
  advance: ['like', 'view', 'retweet', 'comment', 'publish_date'],
  top_sources: ['content_count', 'like', 'view', 'retweet', 'comment'],
  cloud: [
    'hashtag',
    'keywords',
    'entities',
    'person',
    'organ',
    'event',
    'location',
  ],
} as const;

// Sample sources data
const sampleSources = [
  {
    user_name: 'john_doe',
    display_name: 'John Doe',
    platform: 'twitter',
    followers_count: 1500,
    verified: false,
  },
  {
    user_name: 'jane_smith',
    display_name: 'Jane Smith',
    platform: 'instagram',
    followers_count: 2300,
    verified: true,
  },
  {
    user_name: 'tech_news',
    display_name: 'Tech News',
    platform: 'telegram',
    followers_count: 5000,
    verified: false,
  },
];

interface WidgetFormProps {
  dashboardId: string;
  widgetId?: string;
  initialData?: Widget;
  breadcrumbItems: Array<{ label: string; href?: string }>;
  onSubmit: (payload: CreateWidgetPayload) => Promise<void>;
  submitButtonText: string;
  submittingText: string;
  title: string;
  description: string;
}

export default function WidgetForm({
  dashboardId,
  widgetId,
  initialData,
  breadcrumbItems,
  onSubmit,
  submitButtonText,
  submittingText,
  title: pageTitle,
  description,
}: WidgetFormProps) {
  const navigate = useNavigate();
  const [selectedSocials, setSelectedSocials] = useState<string[]>(['twitter']);
  const [selectedReport, setSelectedReport] = useState<string>('');
  const [selectedChartType, setSelectedChartType] = useState<string>('');
  const [selectedTimeInterval, setSelectedTimeInterval] = useState<string>('');
  const [selectedSortField, setSelectedSortField] = useState<string>('');
  const [title, setTitle] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Advanced filters state
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [selectedSources, setSelectedSources] = useState<string[]>([]);
  const [timePeriodValue, setTimePeriodValue] = useState<number>(
    24 * 60 * 60 * 1000
  ); // 24 hours in milliseconds

  // Initialize form with existing data if editing
  useEffect(() => {
    if (initialData) {
      setTitle(initialData.title);

      // Parse report_type to extract base report and sort field
      const reportTypeParts = initialData.report_type.split('%');
      const baseReport = reportTypeParts[0];
      const sortField = reportTypeParts[1] || '';

      setSelectedReport(baseReport);
      setSelectedSortField(sortField);
      setSelectedChartType(initialData.chart_type);
      setSelectedSocials(initialData.params.query?.platform || ['twitter']);
      setSearchQuery(initialData.params.query?.q || '');
      setHashtags(initialData.params.query?.hashtags || []);
      setSelectedSources(initialData.params.query?.sources || []);
      setSelectedTimeInterval(
        (initialData.params.runtime.interval / 60).toString()
      ); // Convert seconds to minutes
      setTimePeriodValue(initialData.params.runtime.gap || 24 * 60 * 60 * 1000);
    }
  }, [initialData]);

  // Define report options
  const reportOptions: SelectOption[] = [
    { label: 'روند انتشار محتوا طی زمان', value: 'process' },
    { label: 'تعداد محتوا', value: 'statistical%post' },
    { label: 'تعداد منابع', value: 'statistical%source' },
    { label: 'تعداد بازدید', value: 'statistical%view' },
    { label: 'تعداد لایک', value: 'statistical%like' },
    { label: 'تعداد بازنشر', value: 'statistical%retweet' },
    { label: 'تعداد نظرات', value: 'statistical%comment' },
    { label: 'برترین منابع (کاربران)', value: 'top_sources' },
    { label: 'تحلیل احساسات محتوای منتشر شده', value: 'sentiments' },
    { label: 'برترین محتوای منتشر شده', value: 'advance' },
    { label: 'موضوعات پرتکرار', value: 'cloud' },
    { label: 'تحلیل عواطف و هیجانات محتوا', value: 'emotion' },
    { label: 'اطلاعات آماری دسته‌های موضوعی محتوا', value: 'categories' },
    { label: 'نسبت محتوای توهین‌آمیز', value: 'offensive' },
    { label: 'نسبت محتوای تبلیغاتی', value: 'advertise' },
  ];

  // Get time interval options based on selected report
  const getTimeIntervalOptions = useCallback((): SelectOption[] => {
    if (!selectedReport) return [];

    const allowedIntervals =
      REPORT_TIME_INTERVALS[
        selectedReport as keyof typeof REPORT_TIME_INTERVALS
      ] || [];

    const allIntervalOptions = [
      { label: '10 ثانیه', value: '0.17' },
      { label: '20 ثانیه', value: '0.33' },
      { label: '30 ثانیه', value: '0.5' },
      { label: '1 دقیقه', value: '1' },
      { label: '3 دقیقه', value: '3' },
      { label: '5 دقیقه', value: '5' },
      { label: '15 دقیقه', value: '15' },
      { label: '30 دقیقه', value: '30' },
      { label: '45 دقیقه', value: '45' },
      { label: '1 ساعت', value: '60' },
      { label: '1.5 ساعت', value: '90' },
      { label: '2 ساعت', value: '120' },
    ];

    return allIntervalOptions.filter((option) =>
      allowedIntervals.includes(parseFloat(option.value))
    );
  }, [selectedReport]);

  // Get sorting field options based on selected report
  const getSortFieldOptions = useCallback((): SelectOption[] => {
    if (!selectedReport) return [];

    const sortFields =
      REPORT_SORTING_FIELDS[
        selectedReport as keyof typeof REPORT_SORTING_FIELDS
      ] || [];

    const sortFieldLabels: Record<string, string> = {
      like: 'لایک',
      view: 'بازدید',
      retweet: 'بازنشر',
      comment: 'نظرات',
      publish_date: 'تاریخ انتشار',
      content_count: 'تعداد محتوا',
      hashtag: 'هشتگ',
      keywords: 'کلمات',
      entities: 'موجودیت',
      person: 'اسامی اشخاص',
      organ: 'اسامی سازمان‌ها',
      event: 'رویدادها',
      location: 'اسامی مکان‌ها',
    };

    return sortFields.map((field) => ({
      value: field,
      label: sortFieldLabels[field] || field,
    }));
  }, [selectedReport]);

  const getChartTypeOptions = useCallback((): SelectOption[] => {
    if (!selectedReport) return [];

    const chartTypes =
      REPORT_CHART_MAPPING[
        selectedReport as keyof typeof REPORT_CHART_MAPPING
      ] || [];

    // Filter chart types based on selected socials
    const filteredChartTypes = chartTypes.filter((chartType) => {
      // If it's a stack chart, only allow if multiple socials are selected
      if (STACK_CHART_TYPES.includes(chartType as any)) {
        return selectedSocials.length > 1;
      }

      // If it's a single chart, only allow if single social is selected
      if (SINGLE_CHART_TYPES.includes(chartType as any)) {
        return selectedSocials.length === 1;
      }

      // For other chart types, allow regardless of social count
      return true;
    });

    return filteredChartTypes.map((type) => ({
      value: type,
      label: getChartTypeLabel(type),
    }));
  }, [selectedReport, selectedSocials]);

  const getChartTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      bar: 'نمودار میله‌ای',
      badge: 'برچسب',
      bar_stack: 'نمودار میله‌ای انباشته',
      bar_comp: 'نمودار میله‌ای مقایسه‌ای',
      line: 'نمودار خطی',
      table: 'جدول',
      bar_stack_hor: 'نمودار میله‌ای افقی انباشته',
      bar_stack_ver: 'نمودار میله‌ای عمودی انباشته',
      radial: 'نمودار شعاعی',
      pie: 'نمودار دایره‌ای',
      donut: 'نمودار حلقه‌ای',
      semi_pie: 'نمودار نیم دایره',
      radar: 'نمودار راداری',
      spider: 'نمودار عنکبوتی',
      wind: 'نمودار بادی',
      list: 'لیست',
      live_list: 'لیست زنده',
      cloud: 'ابر کلمات',
      tree: 'نمودار درختی',
      map_iran: 'نقشه ایران',
      map_world: 'نقشه جهان',
      network_graph: 'گراف شبکه',
    };
    return labels[type] || type;
  };

  // Event handlers
  const handleSocialMediaSelectChange = useCallback(
    (socials: string[]) => {
      setSelectedSocials(socials);

      // Reset chart type if current selection is not valid for new social selection
      if (selectedChartType) {
        const isStackChart = STACK_CHART_TYPES.includes(
          selectedChartType as any
        );
        const isSingleChart = SINGLE_CHART_TYPES.includes(
          selectedChartType as any
        );

        if (
          (isStackChart && socials.length <= 1) ||
          (isSingleChart && socials.length > 1)
        ) {
          setSelectedChartType('');
        }
      }
    },
    [selectedChartType]
  );

  const handleReportChange = useCallback(
    (value: string) => {
      setSelectedReport(value);
      setSelectedChartType(''); // Reset chart type when report changes
      setSelectedTimeInterval(''); // Reset time interval when report changes
      setSelectedSortField(''); // Reset sort field when report changes

      // Filter platforms based on selected report
      const allowedPlatforms =
        REPORT_PLATFORM_MAPPING[
          value as keyof typeof REPORT_PLATFORM_MAPPING
        ] || [];

      if (!allowedPlatforms.includes('all')) {
        // Filter current selected socials to only include allowed platforms
        const filteredSocials = selectedSocials.filter((social) =>
          allowedPlatforms.includes(social as any)
        );

        // If no valid platforms remain, select the first allowed platform
        if (filteredSocials.length === 0 && allowedPlatforms.length > 0) {
          setSelectedSocials([allowedPlatforms[0]]);
        } else if (filteredSocials.length !== selectedSocials.length) {
          setSelectedSocials(filteredSocials);
        }
      }
    },
    [selectedSocials]
  );

  const handleChartTypeChange = useCallback((value: string) => {
    setSelectedChartType(value);
  }, []);

  const handleTimeIntervalChange = useCallback((value: string) => {
    setSelectedTimeInterval(value);
  }, []);

  const handleSortFieldChange = useCallback((value: string) => {
    setSelectedSortField(value);
  }, []);

  const handleTitleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setTitle(e.target.value);
    },
    []
  );

  const handleTimePeriodChange = useCallback((value: number) => {
    setTimePeriodValue(value);
  }, []);

  const validateForm = (): boolean => {
    if (!title.trim()) {
      setError('عنوان نمودار الزامی است');
      return false;
    }
    if (!selectedReport) {
      setError('انتخاب نوع گزارش الزامی است');
      return false;
    }
    if (!selectedChartType) {
      setError('انتخاب نوع نمودار الزامی است');
      return false;
    }
    if (!selectedTimeInterval) {
      setError('انتخاب وقفه زمانی الزامی است');
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    setError('');

    try {
      // Build query params for advanced filters
      const queryParams: any = {};

      if (searchQuery.trim()) {
        queryParams.q = searchQuery.trim();
      }

      if (hashtags.length > 0) {
        queryParams.hashtags = hashtags;
      }

      if (selectedSources.length > 0) {
        queryParams.sources = selectedSources;
      }

      if (selectedSortField) {
        queryParams.sort = selectedSortField;
      }

      // Build report_type with sort field if needed
      let reportType = selectedReport;
      if (
        selectedSortField &&
        (selectedReport === 'advance' || selectedReport === 'cloud')
      ) {
        reportType = `${selectedReport}%${selectedSortField}`;
      }

      console.log('llllllllllllllllllllllllll');
      console.log({
        title: title.trim(),
        chart_type: selectedChartType,
        report_type: reportType,
        params: {
          runtime: {
            interval: parseInt(selectedTimeInterval) * 60, // Convert minutes to seconds
            gap: timePeriodValue, // Add time period from advanced filters
          },
          position: initialData?.params.position || {
            x: 0,
            y: 0,
            width: 5,
            height: 5,
          },
          query: {
            platform: selectedSocials,
            ...queryParams,
          },
        },
      });
      const payload: CreateWidgetPayload = {
        title: title.trim(),
        chart_type: selectedChartType,
        report_type: reportType,
        params: {
          runtime: {
            interval: parseInt(selectedTimeInterval) * 60, // Convert minutes to seconds
            gap: timePeriodValue, // Add time period from advanced filters
          },
          position: initialData?.params.position || {
            x: 0,
            y: 0,
            width: 5,
            height: 5,
          },
          query: {
            platform: selectedSocials,
            ...queryParams,
          },
        },
      };

      await onSubmit(payload);

      // Navigate back to dashboard detail page
      navigate(`/dashboard/${dashboardId}`);
    } catch (error) {
      console.error('Error submitting form:', error);
      setError(error instanceof Error ? error.message : 'خطا در عملیات');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-full p-8">
      <Breadcrumb items={breadcrumbItems} />
      <div className="mx-auto mt-8 w-full max-w-7xl space-y-6">
        {/* Social Media Select - Full Width */}
        <div className="w-full">
          <SocialMediaSelect
            mode="multiple"
            label="بستر‌های مورد نظر خود را انتخاب کنید"
            value={selectedSocials}
            onChange={handleSocialMediaSelectChange}
            error={error}
            allowedPlatforms={
              selectedReport
                ? REPORT_PLATFORM_MAPPING[
                    selectedReport as keyof typeof REPORT_PLATFORM_MAPPING
                  ]
                : undefined
            }
          />
        </div>

        {/* Report Selection Section - Collapsible */}
        <Collapsible
          title="انتخاب نوع گزارش"
          defaultOpen={true}
          className="mt-8"
        >
          {/* Text Input and Time Interval Select - Half Width Each */}
          <div className="mb-6 flex items-center gap-4">
            <div className="flex-6">
              <TextInput
                label="عنوان نمودار"
                placeholder="نمودار برترین محتوا منتشر شده در اینستاگرام"
                value={title}
                onChange={handleTitleChange}
              />
            </div>
            <div className="flex-6">
              <Select
                label="وقفه زمانی به‌روزرسانی گزارش‌ها"
                placeholder={
                  selectedReport
                    ? 'انتخاب کنید'
                    : 'ابتدا نوع گزارش را انتخاب کنید'
                }
                options={getTimeIntervalOptions()}
                value={selectedTimeInterval}
                onChange={handleTimeIntervalChange}
              />
            </div>
          </div>

          {/* Report and Chart Type Selects */}
          <div className="flex items-center gap-4">
            <div className="flex-6">
              <Select
                label="گزارش‌های آماری و هوش مصنوعی"
                placeholder="انتخاب نوع گزارش"
                options={reportOptions}
                value={selectedReport}
                onChange={handleReportChange}
              />
            </div>
            <div className="flex-6">
              <Select
                label="نوع نمودار"
                placeholder={
                  selectedReport
                    ? 'انتخاب نوع نمودار'
                    : 'ابتدا نوع گزارش را انتخاب کنید'
                }
                options={getChartTypeOptions()}
                value={selectedChartType}
                onChange={handleChartTypeChange}
              />
            </div>
          </div>

          {/* Sort Field Select - Show only for reports that need sorting */}
          {getSortFieldOptions().length > 0 && (
            <div className="mt-4">
              <Select
                label="فیلد مرتب‌سازی"
                placeholder="انتخاب فیلد مرتب‌سازی"
                options={getSortFieldOptions()}
                value={selectedSortField}
                onChange={handleSortFieldChange}
              />
            </div>
          )}
        </Collapsible>

        {/* Advanced Filters - Collapsible */}
        <Collapsible title="فیلترهای پیشرفته" defaultOpen={false}>
          <SearchDrawerInput
            label="عبارت جستجو"
            placeholder="عبارت جستجو خود را وارد کنید"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />

          <TagInput
            label="هشتگ‌ها"
            tags={hashtags}
            onTagsChange={setHashtags}
            placeholder="هشتگ‌های مورد نظر را وارد کنید"
          />

          <SourceInput
            label="منابع"
            sources={selectedSources}
            onSourcesChange={setSelectedSources}
            availableSources={sampleSources}
            placeholder="منابع مورد نظر را انتخاب کنید"
          />

          <TimePeriodPicker
            value={timePeriodValue}
            onChange={handleTimePeriodChange}
            label="بازه زمانی نتایج گزارش"
          />
        </Collapsible>

        {/* Error Display */}
        {error && (
          <div className="mt-4 rounded-md bg-red-50 p-4">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {/* Submit Button */}
        <div className="mt-8 flex justify-end gap-4">
          <Button
            variant="secondary"
            onClick={() => navigate(`/dashboard/${dashboardId}`)}
            disabled={isSubmitting}
          >
            انصراف
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? submittingText : submitButtonText}
          </Button>
        </div>
      </div>
    </div>
  );
}
