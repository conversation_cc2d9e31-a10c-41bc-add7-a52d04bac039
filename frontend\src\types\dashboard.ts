export interface Dashboard {
  id: string | number;
  title: string;
  description?: string;
  preview?: string;
  params: {
    runtime: {
      gap: number;
    };
    query: {
      q: string;
      hashtags?: string[];
      sources?: string[];
    };
  };
  widgets?: Widget[];
  created_at?: string;
  updated_at?: string;
}

export interface CreateDashboardPayload {
  title: string;
  description?: string;
  preview?: string;
  params: {
    runtime: {
      gap: number;
    };
    query: {
      q: string;
      report_type?: string;
      hashtags?: string[];
      sources?: string[];
    };
  };
}

export interface CreateDashboardResponse {
  status: string;
  code: number;
  message: string;
  data: Dashboard;
}

export interface DashboardsListResponse {
  status: string;
  code: number;
  message: string;
  data: {
    dashboards: Dashboard[];
  };
}

// Widget interfaces
export interface Widget {
  id: string | number;
  title: string;
  chart_type: string;
  report_type: string;
  description?: string;
  params: {
    runtime: {
      interval: number;
    };
    position: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    query?: {
      q?: string;
      platform?: string[];
      hashtags?: string[];
      sources?: string[];
    };
  };
  created_at?: string;
  updated_at?: string;
  dashboard?: number;
}

export interface CreateWidgetPayload {
  title: string;
  chart_type: string;
  report_type: string;
  params: {
    runtime: {
      interval: number;
      gap?: number;
    };
    position: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    query?: {
      q?: string;
      platform?: string[];
      hashtags?: string[];
      sources?: string[];
      sort?: string;
    };
  };
}

export interface CreateWidgetResponse {
  status: string;
  code: number;
  message: string;
  data: Widget;
}

// Widget data request interface
export interface WidgetDataRequest {
  q: string;
  report_type: string;
  start_date: string;
  end_date: string;
  hashtags: string[];
  sources: string[];
  sort: string;
}

// Widget data response interface
export interface WidgetDataResponse {
  status: string;
  code: number;
  message: string;
  data: WidgetData;
}

// Widget data structure
export interface WidgetData {
  [key: string]: any; // Flexible structure for different chart types
}

// Report Type Data Structures
export interface ProcessData {
  datetime: string;
  timestamp: number;
  count: number;
}

export interface AdvanceData {
  [source: string]: ProcessData[];
}

export interface PostsData {
  id: string;
  content: string;
  author: string;
  datetime: string;
  source: string;
  engagement?: number;
}

export interface CloudData {
  text: string;
  weight: number;
}

export interface SourceInfoData {
  source: string;
  count: number;
  percentage: number;
}

export interface TopSourcesData {
  source: string;
  count: number;
  rank: number;
}

export interface ContentTypeDistributionData {
  type: string;
  count: number;
  percentage: number;
}

export interface SearchInSourceData {
  source: string;
  results: Array<{
    content: string;
    datetime: string;
    relevance: number;
  }>;
}

export interface SimilarPhrasesData {
  phrase: string;
  similarity: number;
  count: number;
}

export interface SourceSuggestData {
  source: string;
  relevance: number;
  description?: string;
}

export interface CategoriesData {
  category: string;
  count: number;
  percentage: number;
}

export interface SentimentsData {
  sentiment: 'positive' | 'negative' | 'neutral';
  count: number;
  percentage: number;
}

export interface StatisticalData {
  metric: string;
  value: number;
  change?: number;
  changeType?: 'increase' | 'decrease' | 'stable';
}

export interface SearchSuggestData {
  query: string;
  relevance: number;
  count: number;
}

export interface OffensiveData {
  content: string;
  severity: 'low' | 'medium' | 'high';
  datetime: string;
  source: string;
}

export interface AdvertiseData {
  content: string;
  brand?: string;
  datetime: string;
  source: string;
  engagement?: number;
}

export interface GraphNodeData {
  id: string;
  name: string;
  value: number;
  group?: string;
}

export interface GraphLinkData {
  source: string;
  target: string;
  value: number;
}

export interface InfluenceGraphData {
  nodes: GraphNodeData[];
  links: GraphLinkData[];
}

export interface ImpactGraphData {
  nodes: GraphNodeData[];
  links: GraphLinkData[];
}

export interface RelatedContentData {
  content: string;
  similarity: number;
  datetime: string;
  source: string;
}

export interface EmotionData {
  emotion: string;
  count: number;
  percentage: number;
}

export interface TrendsData {
  datetime: string;
  value: number;
  trend: 'up' | 'down' | 'stable';
}

export interface TrendsGraphData {
  [category: string]: TrendsData[];
}

export interface TrendsStatisticalData {
  period: string;
  average: number;
  peak: number;
  growth: number;
}

export interface TrendsContentAgeData {
  ageRange: string;
  count: number;
  percentage: number;
}

export interface TrendsAccountCredentialData {
  credentialType: string;
  count: number;
  verified: boolean;
}

export interface OpinionGraphData {
  nodes: Array<{
    id: string;
    opinion: string;
    strength: number;
    group: string;
  }>;
  links: Array<{
    source: string;
    target: string;
    agreement: number;
  }>;
}

export interface OpinionEmotionData {
  opinion: string;
  emotions: EmotionData[];
}

export interface OpinionSentimentsData {
  opinion: string;
  sentiments: SentimentsData[];
}

export interface OpinionAgeData {
  opinion: string;
  ageGroups: Array<{
    ageRange: string;
    count: number;
    percentage: number;
  }>;
}

export interface OpinionGenderData {
  opinion: string;
  genders: Array<{
    gender: string;
    count: number;
    percentage: number;
  }>;
}

export interface OpinionCategoriesData {
  opinion: string;
  categories: CategoriesData[];
}

export interface OpinionTopSourcesData {
  opinion: string;
  sources: TopSourcesData[];
}

// Union type for all possible report data types
export type ReportData =
  | { twitter: ProcessData[] }
  | { [source: string]: ProcessData[] }
  | PostsData[]
  | CloudData[]
  | SourceInfoData[]
  | TopSourcesData[]
  | ContentTypeDistributionData[]
  | SearchInSourceData[]
  | SimilarPhrasesData[]
  | SourceSuggestData[]
  | CategoriesData[]
  | SentimentsData[]
  | StatisticalData[]
  | SearchSuggestData[]
  | OffensiveData[]
  | AdvertiseData[]
  | InfluenceGraphData
  | ImpactGraphData
  | RelatedContentData[]
  | EmotionData[]
  | TrendsData[]
  | TrendsGraphData
  | TrendsStatisticalData[]
  | TrendsContentAgeData[]
  | TrendsAccountCredentialData[]
  | OpinionGraphData
  | OpinionEmotionData[]
  | OpinionSentimentsData[]
  | OpinionAgeData[]
  | OpinionGenderData[]
  | OpinionCategoriesData[]
  | OpinionTopSourcesData[];
